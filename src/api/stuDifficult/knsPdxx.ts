/*
 * @Description: 困难学生评定信息管理API
 * @Autor: panmy
 * @Date: 2025-08-05 10:00:00
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 10:00:00
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/knsPdxx',
}

/**
 * 获取评定信息审核列表（分页查询）
 * @param data 查询参数
 * @returns 分页数据
 */
export function getKnsPdxxList(data: any) {
  return defHttp.get({ url: Api.Prefix + '/getList', data });
}

/**
 * 下载模板
 * @returns 模板文件
 */
export function downloadTemplate() {
  return defHttp.get({ url: Api.Prefix + '/downloadTemplate' });
}

/**
 * 导入评定信息
 * @param data 导入数据
 * @returns 导入结果
 */
export function importPdxx(data: any) {
  return defHttp.post({ url: Api.Prefix + '/importPdxx', data });
}

/**
 * 导出评定信息
 * @param data 导出参数
 * @returns 导出文件
 */
export function exportPdxx(data: any) {
  return defHttp.get({ url: Api.Prefix + '/export', data });
}

/**
 * 批量删除评定信息
 * @param data 删除参数，包含要删除的ID数组
 * @returns 删除结果
 */
export function batchRemovePdxx(data: any) {
  return defHttp.delete({ url: Api.Prefix + '/batchRemove', data });
}

/**
 * 获取单个评定信息详情
 * @param id 评定信息ID
 * @returns 详情数据
 */
export function getKnsPdxxDetail(id: string | number) {
  return defHttp.get({ url: Api.Prefix + `/${id}` });
}

/**
 * 保存评定信息
 * @param data 保存数据
 * @returns 保存结果
 */
export function saveKnsPdxx(data: any) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

/**
 * 更新评定信息
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateKnsPdxx(data: any) {
  return defHttp.put({ url: Api.Prefix + `/edit/${data.id}`, data });
}

/**
 * 删除单个评定信息
 * @param id 评定信息ID
 * @returns 删除结果
 */
export function removeKnsPdxx(id: string | number) {
  return defHttp.delete({ url: Api.Prefix + `/remove/${id}` });
}
