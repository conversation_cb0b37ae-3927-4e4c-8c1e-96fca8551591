<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()"> 提名 </a-button>

            <a-button type="default" preIcon="icon-ym icon-ym-btn-upload" @click="handleImport"> 导入 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-btn-download" @click="handleExport"> 导出 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-delete" @click="handelBatchRemove"> 删除 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-report-icon-preview-print" @click="handlePrint"> 批量打印 </a-button>
            <a-button type="default" preIcon="icon-ym icon-ym-report-icon-preview-print" @click="handlePrintTotal"> 汇总表打印 </a-button>
          </template>
          <!-- <template #toolbar>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()"> 设置 </a-button>
          </template> -->
          <template #bodyCell="{ column, record }">
            <!-- 审核状态列 -->
            <template v-if="column.dataIndex === 'auditStatus'">
              <a-tag :color="getTagColor(record.auditStatus)">
                {{ getAuditStatusText(record.auditStatus) }}
              </a-tag>
            </template>
            <!-- 日期列格式化 -->
            <template v-else-if="column.dataIndex === 'applyTime' || column.dataIndex === 'yearFee'">
              {{ formatDate(record[column.dataIndex]) }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" :dropDownActions="[]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 新增/申请表 -->
    <AddForm @register="registerAddPopup" @reload="reload" />
    <!-- 问卷结果 -->
    <QuestionForm @register="registerQuestionForm" />
    <!-- 导出模态框 -->
    <ExportModal @register="registerExportModal" />
    <!-- 导入模态框 -->
    <ImportModal @register="registerImportModal" @reload="reload" />
    <!-- 打印选择模态框 -->
    <PrintSelect @register="registerPrintSelect" @change="handleShowBrowse" />
    <!-- 打印预览模态框 -->
    <PrintBrowse @register="registerPrintBrowse" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage, useConfirm } from '@/hooks/web/useMessage';
  import { BasicTable, useTable, BasicColumn, TableAction } from '@/components/Table';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  // import { getDifficultyStudentList, deleteDifficultyStudents } from '@/api/student/difficulty';
  import AddForm from './components/AddForm.vue';
  import QuestionForm from '@/views/stuDifficult/components/QuestionForm.vue';
  import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import PrintSelect from '@/components/PrintDesign/printSelect/index.vue';
  import PrintBrowse from '@/components/PrintDesign/printBrowse/index.vue';
  import { Icon } from '@/components/Icon';

  const { createMessage, createConfirm } = useMessage();

  const columns: BasicColumn[] = [
    {
      title: '学号',
      dataIndex: 'studentId',
      width: 120,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'studentName',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '院系',
      dataIndex: 'department',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'major',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'class',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'currentGrade',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      width: 150,
      resizable: true,
      ellipsis: true,
      sorter: true,
    },
    {
      title: '评定学年',
      dataIndex: 'assessmentYear',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请困难等级',
      dataIndex: 'applyDifficultyLevel',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '评定困难等级',
      dataIndex: 'assessmentDifficultyLevel',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '是否有效',
      dataIndex: 'isValid',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '民族',
      dataIndex: 'ethnicity',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '身份证件类型',
      dataIndex: 'idType',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '身份证件号',
      dataIndex: 'idNumber',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '出生地',
      dataIndex: 'birthPlace',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '国家地区',
      dataIndex: 'country',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '港澳台侨',
      dataIndex: '港澳台侨',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '政治面貌',
      dataIndex: 'politicalStatus',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '信仰宗教',
      dataIndex: 'religion',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '婚姻状态',
      dataIndex: 'maritalStatus',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '生源地',
      dataIndex: 'origin',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '培养层次',
      dataIndex: 'educationLevel',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '电子信箱',
      dataIndex: 'email',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: 'QQ号',
      dataIndex: 'qq',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学籍状态',
      dataIndex: 'status',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'right',
      onFilter: (value: string, record: Recordable) => record.auditStatus.includes(value),
    },
  ];

  const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    // api: getDifficultyStudentList,
    dataSource: [
      {
        action: '申请表',
        auditStatus: '待学校审核',
        studentId: '15030162',
        studentName: '许一乔',
        department: '法学院',
        major: '法律史',
        class: '法律史15',
        currentGrade: '2015',
        applyTime: '2018-10-16',
        assessmentYear: '2015',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '否',
      },
      {
        action: '申请表',
        auditStatus: '待辅导员审核',
        studentId: '18030441',
        studentName: '杨诚',
        department: '公共管理学院',
        major: '公共管理系',
        class: '公共管理18',
        currentGrade: '2018',
        applyTime: '2022-10-09',
        assessmentYear: '2022-2023学年',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '待辅导员审核',
        studentId: '204035',
        studentName: '王伟',
        department: '医科学院',
        major: '病原生物学',
        class: '病原生物学2班',
        currentGrade: '2020',
        applyTime: '2022-10-09',
        assessmentYear: '2022-2023学年',
        applyDifficultyLevel: '困难',
        assessmentDifficultyLevel: '困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '辅导员审核不通过',
        studentId: '18230201',
        studentName: '胡诚',
        department: '历史学院',
        major: '历史学',
        class: '历史学18',
        currentGrade: '2018',
        applyTime: '2022-10-09',
        assessmentYear: '2022-2023学年',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '待学校审核',
        studentId: '15030347',
        studentName: '袁航',
        department: '资源与环境科学学院',
        major: '农业资源与环境',
        class: '农业资源与环境1班',
        currentGrade: '2015',
        applyTime: '2018-10-16',
        assessmentYear: '2015',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '否',
      },
      {
        action: '申请表',
        auditStatus: '待学校审核',
        studentId: '18020603',
        studentName: '陈雨琦',
        department: '医科学院',
        major: '动物医学',
        class: '动物医学18',
        currentGrade: '2018',
        applyTime: '2021-08-23',
        assessmentYear: '2021-2022学年',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '否',
      },
      {
        action: '申请表',
        auditStatus: '待辅导员审核',
        studentId: '194045',
        studentName: '甘沁川',
        department: '医科学院',
        major: '病原生物学',
        class: '病原生物学1班',
        currentGrade: '2019',
        applyTime: '2022-10-09',
        assessmentYear: '2022-2023学年',
        applyDifficultyLevel: '特别困难',
        assessmentDifficultyLevel: '特别困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '草稿',
        studentId: '18010545',
        studentName: '钟慧',
        department: '法学院',
        major: '法律史',
        class: '法律史18',
        currentGrade: '2018',
        applyTime: '2018-10-16',
        assessmentYear: '2018-2019学年',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '待学院/书院审核',
        studentId: '20170026',
        studentName: '姜少波',
        department: '历史学院',
        major: '历史学',
        class: '历史学16',
        currentGrade: '2016',
        applyTime: '2018-10-16',
        assessmentYear: '2016',
        applyDifficultyLevel: '一般困难',
        assessmentDifficultyLevel: '一般困难',
        isValid: '是',
      },
      {
        action: '申请表',
        auditStatus: '待学校审核',
        studentId: '18030438',
        studentName: '王宇',
        department: '公共管理学院',
        major: '公共管理系',
        class: '公共管理18',
        currentGrade: '2018',
        applyTime: '2021-08-23',
        assessmentYear: '2021-2022学年',
        applyDifficultyLevel: '特别困难',
        assessmentDifficultyLevel: '特别困难',
        isValid: '否',
      },
    ],
    rowSelection: {
      type: 'checkbox',
    },
    columns,
    useSearchForm: true,
    formConfig: getFormConfig(),
    showTableSetting: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '申请表',
        onClick: () => {
          addOrUpdateHandle(record);
        },
      },
      {
        label: '问卷结果',
        onClick: () => {
          openQuestionForm(true, { ...record, opType: 2 });
        },
      },
    ];
  }
  function getFormConfig() {
    return {
      labelWidth: 100,
      schemas: [
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            options: [],
          },
        },
        {
          field: 'assessmentYear',
          label: '评定学年',
          component: 'Select',
          componentProps: {
            options: [
              { fullName: '全部', value: '全部' },
              { fullName: '2024-2025学年', value: '2024-2025' },
              { fullName: '2023-2024学年', value: '2023-2024' },
              { fullName: '2022-2023学年', value: '2022-2023' },
              { fullName: '2021-2022学年', value: '2021-2022' },
              { fullName: '2020-2021学年', value: '2020-2021' },
            ],
          },
        },
      ],
    };
  }

  // 获取标签颜色
  function getTagColor(status: string) {
    const colorMap = {
      待班主任审核: 'processing',
      待辅导员审核: 'processing',
      待班级审核: 'processing',
      '待学院/书院审核': 'processing',
      待学校审核: 'processing',
      通过: 'success',
      不通过: 'error',
      已撤回: 'default',
    };
    return colorMap[status] || 'default';
  }

  // 获取审核状态文本
  function getAuditStatusText(status: string) {
    const textMap = {
      待班主任审核: '待班主任审核',
      待辅导员审核: '待辅导员审核',
      待班级审核: '待班级审核',
      '待学院/书院审核': '待学院/书院审核',
      待学校审核: '待学校审核',
      通过: '通过',
      不通过: '不通过',
      已撤回: '已撤回',
    };
    return textMap[status] || status;
  }

  // 日期格式化
  function formatDate(dateString: string) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) {
      createMessage.error('请至少选择一条数据');
      return;
    }
    createConfirm({
      title: '确认删除',
      content: '是否确认删除选中的困难生信息?',
      onOk: () => {
        // deleteDifficultyStudents(ids)
        //   .then(() => {
        //     createMessage.success('删除成功');
        //     clearSelectedRowKeys();
        //     reload();
        //   })
        //   .catch(err => {
        //     console.error(err);
        //   });
      },
    });
  }

  // 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    openExportModal(true, { columnList: columns });
  }

  // 导入
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  function handleImport() {
    openImportModal(true);
  }

  // 打印
  const [registerPrintSelect, { openModal: openPrintSelect }] = useModal();
  const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
  function handlePrint() {
    return createMessage.info('汇总表打印功能开发中');
    openPrintSelect(true);
  }
  function handleShowBrowse() {
    openPrintBrowse(true);
  }

  // 汇总表打印
  function handlePrintTotal() {
    createMessage.info('汇总表打印功能开发中');
  }

  // 新增/申请表
  const [registerAddPopup, { openPopup: openAddPopup }] = usePopup();
  function addOrUpdateHandle(record) {
    openAddPopup(true, record);
  }
  // 问卷结果
  const [registerQuestionForm, { openPopup: openQuestionForm }] = usePopup();
</script>
